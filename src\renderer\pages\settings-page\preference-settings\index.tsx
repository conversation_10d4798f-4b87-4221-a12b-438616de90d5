import { ChatSettings } from "./chat-settings";
import { DisplayAppStore } from "./display-app-store";
import ModelSelect from "./model-select";
import { ParseUrlSwitch } from "./parse-url-switch";
import { SearchService } from "./search-service";
import { StreamOutput } from "./stream-output";
import { TitleGenerationTiming } from "./title-generation-timing";
import TitleModelSelect from "./title-model-select";

export function PreferenceSettings() {
  return (
    <div className="flex flex-1 flex-col overflow-y-auto py-[18px]">
      <div className="mx-auto flex flex-col gap-4">
        <ChatSettings />

        <SearchService />

        <ParseUrlSwitch />

        <StreamOutput />

        <DisplayAppStore />

        <ModelSelect />

        <TitleModelSelect />

        <TitleGenerationTiming />
      </div>
    </div>
  );
}
