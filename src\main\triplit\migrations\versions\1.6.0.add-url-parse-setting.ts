/** biome-ignore-all lint/suspicious/noExplicitAny: ignore all */
import { FieldMigration } from "@shared/triplit/migrations/base-migration";
import type {
  CollectionMigrationConfig,
  DataFillFunction,
  MigrationContext,
} from "@shared/triplit/migrations/types";

export class AddUrlParseSettingMigration extends FieldMigration {
  public readonly version = "1.6.0";
  public readonly description = "Add URL parse setting (enableUrlParse) to settings collection";
  public readonly dependencies: string[] = [];

  private fillUrlParseSetting: DataFillFunction = async (
    _client,
    entity,
    context,
  ) => {
    const { logger } = context;

    // Set default value for URL parse setting
    const enableUrlParse = true; // Default to enable URL parsing

    logger.debug(`Setting default URL parse setting for settings entity ${entity.id}`);

    return {
      enableUrlParse,
    };
  };

  protected getMigrationConfig(): CollectionMigrationConfig[] {
    return [
      {
        collectionName: "settings",
        fields: [
          {
            fieldName: "enableUrlParse",
            fillFunction: this.fillUrlParseSetting,
          },
        ],
        customMigration: async (context: MigrationContext) => {
          const { logger } = context;
          logger.info("Running custom migration logic for URL parse setting");
          logger.info("URL parsing will be enabled by default");
        },
      },
    ];
  }
}
